from flask import Flask, render_template, request, redirect, session
import random
import smtplib

app = Flask(__name__)
app.secret_key = 'any_random_secret'

# Fake user data
user = {
    'username': 'admin',
    'password': 'admin123',
    'email': '<EMAIL>'  # put your email here
}

@app.route('/', methods=['GET', 'POST'])
def login():
    if request.method == 'POST':
        entered_username = request.form['username']
        entered_password = request.form['password']
        
        if entered_username == user['username'] and entered_password == user['password']:
            otp = str(random.randint(100000, 999999))
            session['otp'] = otp
            
            # Send OTP to your email
            sender = "<EMAIL>"
            password = "your_app_password"  # you get this from Gmail settings
            receiver = user['email']
            message = f"Subject: Your OTP\n\nYour OTP is: {otp}"

            server = smtplib.SMTP("smtp.gmail.com", 587)
            server.starttls()
            server.login(sender, password)
            server.sendmail(sender, receiver, message)
            server.quit()

            return redirect('/verify')

    return render_template('login.html')
    